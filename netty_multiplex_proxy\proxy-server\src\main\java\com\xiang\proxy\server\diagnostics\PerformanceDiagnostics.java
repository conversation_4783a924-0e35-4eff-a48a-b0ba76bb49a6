package com.xiang.proxy.server.diagnostics;

import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.outbound.impl.AsyncTcpDirectOutboundHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能诊断工具
 * 监控队列状态、连接状态和数据流性能
 */
public class PerformanceDiagnostics {
    private static final Logger logger = LoggerFactory.getLogger(PerformanceDiagnostics.class);
    
    private final ProxyProcessor proxyProcessor;
    private final AsyncTcpDirectOutboundHandler outboundHandler;
    private final ScheduledExecutorService scheduler;
    
    // 性能计数器
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong totalResponses = new AtomicLong(0);
    private final AtomicLong totalErrors = new AtomicLong(0);
    private final AtomicLong totalLatency = new AtomicLong(0);
    
    public PerformanceDiagnostics(ProxyProcessor proxyProcessor, AsyncTcpDirectOutboundHandler outboundHandler) {
        this.proxyProcessor = proxyProcessor;
        this.outboundHandler = outboundHandler;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "performance-diagnostics");
            t.setDaemon(true);
            return t;
        });
    }
    
    /**
     * 启动性能监控
     */
    public void start() {
        // 每30秒输出一次性能报告
        scheduler.scheduleAtFixedRate(this::generatePerformanceReport, 30, 30, TimeUnit.SECONDS);
        
        // 每5分钟检查一次潜在问题
        scheduler.scheduleAtFixedRate(this::checkPerformanceIssues, 300, 300, TimeUnit.SECONDS);
        
        logger.info("性能诊断工具已启动");
    }
    
    /**
     * 停止性能监控
     */
    public void stop() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("性能诊断工具已停止");
    }
    
    /**
     * 生成性能报告
     */
    private void generatePerformanceReport() {
        try {
            StringBuilder report = new StringBuilder();
            report.append("\n=== 性能诊断报告 ===\n");
            
            // 队列状态
            if (proxyProcessor != null) {
                report.append("队列状态: ").append(proxyProcessor.getQueueStats()).append("\n");
                report.append("活跃连接数: ").append(proxyProcessor.getActiveConnectionCount()).append("\n");
            }
            
            // 连接状态
            if (outboundHandler != null) {
                report.append("Outbound连接数: ").append(outboundHandler.getActiveConnectionCount()).append("\n");
                report.append("连接统计: ").append(outboundHandler.getStats()).append("\n");
            }
            
            // 性能指标
            long requests = totalRequests.get();
            long responses = totalResponses.get();
            long errors = totalErrors.get();
            long avgLatency = requests > 0 ? totalLatency.get() / requests : 0;
            
            report.append("总请求数: ").append(requests).append("\n");
            report.append("总响应数: ").append(responses).append("\n");
            report.append("总错误数: ").append(errors).append("\n");
            report.append("平均延迟: ").append(avgLatency).append("ms\n");
            
            if (requests > 0) {
                double errorRate = (double) errors / requests * 100;
                report.append("错误率: ").append(String.format("%.2f%%", errorRate)).append("\n");
            }
            
            report.append("========================\n");
            
            logger.info(report.toString());
            
        } catch (Exception e) {
            logger.warn("生成性能报告时发生异常", e);
        }
    }
    
    /**
     * 检查性能问题
     */
    private void checkPerformanceIssues() {
        try {
            // 检查错误率
            long requests = totalRequests.get();
            long errors = totalErrors.get();
            
            if (requests > 100) { // 至少有100个请求才进行检查
                double errorRate = (double) errors / requests * 100;
                
                if (errorRate > 10) {
                    logger.warn("⚠️ 高错误率检测: {:.2f}% (错误数: {}, 总请求数: {})", 
                            errorRate, errors, requests);
                }
                
                if (errorRate > 20) {
                    logger.error("🚨 严重错误率: {:.2f}% - 建议检查系统状态", errorRate);
                }
            }
            
            // 检查平均延迟
            if (requests > 0) {
                long avgLatency = totalLatency.get() / requests;
                if (avgLatency > 1000) {
                    logger.warn("⚠️ 高延迟检测: 平均延迟 {}ms", avgLatency);
                }
                
                if (avgLatency > 5000) {
                    logger.error("🚨 严重延迟: 平均延迟 {}ms - 建议检查网络和系统负载", avgLatency);
                }
            }
            
            // 检查队列积压
            if (proxyProcessor != null) {
                String queueStats = proxyProcessor.getQueueStats().toString();
                if (queueStats.contains("积压") || queueStats.contains("满")) {
                    logger.warn("⚠️ 队列积压检测: {}", queueStats);
                }
            }
            
        } catch (Exception e) {
            logger.warn("检查性能问题时发生异常", e);
        }
    }
    
    /**
     * 记录请求
     */
    public void recordRequest() {
        totalRequests.incrementAndGet();
    }
    
    /**
     * 记录响应
     */
    public void recordResponse() {
        totalResponses.incrementAndGet();
    }
    
    /**
     * 记录错误
     */
    public void recordError() {
        totalErrors.incrementAndGet();
    }
    
    /**
     * 记录延迟
     */
    public void recordLatency(long latencyMs) {
        totalLatency.addAndGet(latencyMs);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalRequests.set(0);
        totalResponses.set(0);
        totalErrors.set(0);
        totalLatency.set(0);
        logger.info("性能统计信息已重置");
    }
    
    /**
     * 获取当前统计信息
     */
    public String getStats() {
        long requests = totalRequests.get();
        long responses = totalResponses.get();
        long errors = totalErrors.get();
        long avgLatency = requests > 0 ? totalLatency.get() / requests : 0;
        double errorRate = requests > 0 ? (double) errors / requests * 100 : 0;
        
        return String.format("请求: %d, 响应: %d, 错误: %d, 错误率: %.2f%%, 平均延迟: %dms",
                requests, responses, errors, errorRate, avgLatency);
    }
}
