package com.xiang.proxy.server.outbound.impl;

import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.outbound.OutboundConfig;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundStatistics;
import com.xiang.proxy.server.outbound.OutboundHandlerType;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollSocketChannel;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ConnectException;
import java.net.UnknownHostException;
import java.nio.channels.ClosedChannelException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * TCP直连Outbound处理器
 * 直接连接到目标服务器
 */
public class TcpDirectOutboundHandler implements OutboundHandler {
    private static final Logger logger = LoggerFactory.getLogger(TcpDirectOutboundHandler.class);

    private final String outboundId;
    private final OutboundConfig config;
    private final AtomicLong connectionCounter = new AtomicLong(0);
    private final AtomicLong successCounter = new AtomicLong(0);
    private final AtomicLong failureCounter = new AtomicLong(0);
    private volatile HealthStatus healthStatus = HealthStatus.HEALTHY;

    // Bootstrap模板，避免每次连接都创建新对象
    private final Bootstrap bootstrapTemplate;

    public TcpDirectOutboundHandler(String outboundId, OutboundConfig config) {
        this.outboundId = outboundId;
        this.config = config != null ? config : new OutboundConfig();
        this.bootstrapTemplate = createBootstrapTemplate();
    }

    /**
     * 创建Bootstrap模板
     * 预配置所有通用选项，避免每次连接都重复配置
     */
    private Bootstrap createBootstrapTemplate() {
        //PlatformDependent.isWindows()
        Class<? extends SocketChannel> socketChannelClazz = NioSocketChannel.class;

        if (Epoll.isAvailable()) {
            socketChannelClazz = EpollSocketChannel.class;
        }

        Bootstrap template = new Bootstrap();
        template.channel(socketChannelClazz)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeout())
                .option(ChannelOption.SO_RCVBUF, config.getReceiveBufferSize())
                .option(ChannelOption.SO_SNDBUF, config.getSendBufferSize())
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK,
                        new WriteBufferWaterMark(config.getLowWaterMark(), config.getHighWaterMark()))
                .handler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel ch) throws Exception {
                        // 连接建立后再设置处理器
                    }
                });
        return template;
    }

    @Override
    public CompletableFuture<OutboundConnection> connect(ProxyRequest request) {
        CompletableFuture<OutboundConnection> future = new CompletableFuture<>();
        long startTime = System.currentTimeMillis();
        connectionCounter.incrementAndGet();

        logger.debug("开始建立直连连接: {} -> {}:{}",
                request.getRequestId(), request.getTargetHost(), request.getTargetPort());

        // 直接创建新连接（已移除ConnectionPool）
        createNewConnection(request, future, startTime);
        return future;
    }

    /**
     * 创建新的连接
     * 使用Bootstrap模板克隆，避免重复创建和配置
     */
    private void createNewConnection(ProxyRequest request, CompletableFuture<OutboundConnection> future,
                                     long startTime) {
        EventLoopGroup eventLoopGroup = request.getClientChannel().eventLoop();

        // 克隆Bootstrap模板并设置EventLoopGroup
        Bootstrap bootstrap = bootstrapTemplate.clone().group(eventLoopGroup);

        ChannelFuture connectFuture = bootstrap.connect(request.getTargetHost(), request.getTargetPort());

        connectFuture.addListener((ChannelFutureListener) channelFuture -> {
            long connectTime = System.currentTimeMillis() - startTime;

            if (channelFuture.isSuccess()) {
                Channel backendChannel = channelFuture.channel();
                logger.debug("直连连接建立成功: {} -> {}:{}, 耗时: {}ms",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(), connectTime);

                try {
                    OutboundConnection connection = createOutboundConnection(backendChannel, request, startTime);
                    connection.setAttribute(OutboundConnection.Attributes.CONNECT_TIME, connectTime);

                    future.complete(connection);
                    successCounter.incrementAndGet();
                    updateHealthStatus(true);

                } catch (Exception e) {
                    logger.error("创建OutboundConnection失败: {}", request.getRequestId(), e);
                    backendChannel.close();
                    future.completeExceptionally(e);
                    failureCounter.incrementAndGet();
                    updateHealthStatus(false);
                }

            } else {
                Throwable cause = channelFuture.cause();
                logger.warn("直连连接建立失败: {} -> {}:{}, 耗时: {}ms, 原因: {}",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(),
                        connectTime, cause.getMessage());

                future.completeExceptionally(cause);
                failureCounter.incrementAndGet();
                updateHealthStatus(false);
            }
        });
    }

    /**
     * 创建OutboundConnection对象
     */
    private OutboundConnection createOutboundConnection(Channel backendChannel, ProxyRequest request, long startTime) {
        return OutboundConnection.builder()
                .backendChannel(backendChannel)
                .target(request.getTargetHost(), request.getTargetPort())
                .protocol(request.getProtocol())
                .createTime(startTime)
                .attribute(OutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                .attribute(OutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                .attribute(OutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                .build();
    }



    @Override
    public CompletableFuture<Void> sendData(OutboundConnection connection, ByteBuf data) {
        CompletableFuture<Void> future = new CompletableFuture<>();

        // 验证连接状态
        if (connection == null) {
            future.completeExceptionally(new IllegalArgumentException("Connection is null"));
            return future;
        }

        if (!connection.isActive()) {
            future.completeExceptionally(new IllegalStateException("Connection is not active"));
            return future;
        }

        Channel backendChannel = connection.getBackendChannel();
        if (backendChannel == null) {
            logger.warn("后端连接通道为空: {}", connection.getConnectionId());
            connection.markInactive();
            future.completeExceptionally(new IllegalStateException("Backend channel is null"));
            return future;
        }

        if (!backendChannel.isActive() || !backendChannel.isWritable()) {
            logger.warn("后端连接通道已关闭: {}", connection.getConnectionId());
            connection.markInactive();
            future.completeExceptionally(new IllegalStateException("Backend channel is not active"));
            return future;
        }

        // 验证数据
        if (data == null || !data.isReadable()) {
            logger.debug("数据为空或不可读: {}", connection.getConnectionId());
            future.complete(null);
            return future;
        }

        // 保留数据引用并发送
        int dataSize = data.readableBytes();
        data.retain();

        try {
            ChannelFuture writeFuture = backendChannel.writeAndFlush(data);

            writeFuture.addListener((ChannelFutureListener) channelFuture -> {
                try {
                    if (channelFuture.isSuccess()) {
                        connection.addBytesTransferred(dataSize);
                        logger.debug("数据发送成功: {}, bytes={}", connection.getConnectionId(), dataSize);
                        future.complete(null);
                    } else {
                        Throwable cause = channelFuture.cause();
                        handleSendDataFailure(connection, dataSize, cause);
                        future.completeExceptionally(cause);
                    }
                } finally {
                    data.release(); // 确保释放引用
                }
            });

        } catch (Exception e) {
            data.release(); // 发生异常时释放引用
            handleSendDataFailure(connection, dataSize, e);
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 处理数据发送失败
     * 根据错误类型进行分类处理，减少重复日志
     */
    private void handleSendDataFailure(OutboundConnection connection, int dataSize, Throwable cause) {
        // 标记连接为不活跃状态
        connection.markInactive();

        // 根据错误类型进行分类处理
        String errorType = classifyConnectionError(cause);
        String errorMessage = formatErrorMessage(cause);

        // 使用不同的日志级别，避免Connection reset by peer的ERROR级别日志
        if (isConnectionResetError(cause)) {
            // Connection reset by peer是常见的网络问题，降级为DEBUG
            logger.debug("连接被远程主机重置: {}, bytes={}, target={}:{}",
                    connection.getConnectionId(), dataSize,
                    connection.getTargetHost(), connection.getTargetPort());
        } else if (isClosedChannelError(cause)) {
            logger.debug("通道已关闭: {}, bytes={}, target={}:{}",
                    connection.getConnectionId(), dataSize,
                    connection.getTargetHost(), connection.getTargetPort());
        } else if (isNetworkTimeoutError(cause)) {
            logger.warn("网络超时: {}, bytes={}, target={}:{}, error={}",
                    connection.getConnectionId(), dataSize,
                    connection.getTargetHost(), connection.getTargetPort(), errorMessage);
        } else {
            // 其他类型的错误保持ERROR级别
            logger.error("发送数据失败: {}, bytes={}, target={}:{}, type={}",
                    connection.getConnectionId(), dataSize,
                    connection.getTargetHost(), connection.getTargetPort(), errorType, cause);
        }

        // 更新统计信息
        failureCounter.incrementAndGet();
        updateHealthStatus(false);
    }

    /**
     * 判断是否为连接重置错误
     */
    private boolean isConnectionResetError(Throwable cause) {
        if (cause instanceof IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("Connection reset by peer") ||
                    message.contains("Connection reset") ||
                    message.contains("远程主机强迫关闭了一个现有的连接"));
        }
        return false;
    }

    /**
     * 判断是否为通道已关闭错误
     */
    private boolean isClosedChannelError(Throwable cause) {
        // 检查通道关闭异常
        if (cause instanceof ClosedChannelException) {
            return true;
        }
        if (cause instanceof IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("Channel is closed") ||
                    message.contains("通道已关闭") ||
                    message.contains("Connection is closed"));
        }
        return false;
    }

    /**
     * 判断是否为网络超时错误
     */
    private boolean isNetworkTimeoutError(Throwable cause) {
        if (cause instanceof TimeoutException) {
            return true;
        }
        if (cause instanceof IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("timeout") ||
                    message.contains("超时") ||
                    message.contains("Read timed out") ||
                    message.contains("Write timed out"));
        }
        return false;
    }

    /**
     * 分类连接错误类型
     */
    private String classifyConnectionError(Throwable cause) {
        if (isConnectionResetError(cause)) {
            return "CONNECTION_RESET";
        } else if (isClosedChannelError(cause)) {
            return "CHANNEL_CLOSED";
        } else if (isNetworkTimeoutError(cause)) {
            return "NETWORK_TIMEOUT";
        } else if (cause instanceof ConnectException) {
            return "CONNECTION_REFUSED";
        } else if (cause instanceof UnknownHostException) {
            return "UNKNOWN_HOST";
        } else if (cause instanceof IOException) {
            return "IO_ERROR";
        } else {
            return "UNKNOWN_ERROR";
        }
    }

    /**
     * 格式化错误消息
     */
    private String formatErrorMessage(Throwable cause) {
        if (cause == null) {
            return "Unknown error";
        }
        String message = cause.getMessage();
        return message != null ? message : cause.getClass().getSimpleName();
    }

    @Override
    public CompletableFuture<Void> closeConnection(OutboundConnection connection) {
        CompletableFuture<Void> future = new CompletableFuture<>();

        if (connection == null) {
            future.complete(null);
            return future;
        }

        Channel backendChannel = connection.getBackendChannel();

        if (backendChannel != null && backendChannel.isActive()) {
            // 直接关闭连接（已移除ConnectionPool）
            closeChannelDirectly(backendChannel, connection.getConnectionId(), future);
        } else {
            future.complete(null);
        }

        return future;
    }

    /**
     * 直接关闭连接通道
     */
    private void closeChannelDirectly(Channel channel, String connectionId, CompletableFuture<Void> future) {
        ChannelFuture closeFuture = channel.close();
        closeFuture.addListener((ChannelFutureListener) channelFuture -> {
            if (channelFuture.isSuccess()) {
                logger.debug("连接关闭成功: {}", connectionId);
                future.complete(null);
            } else {
                logger.warn("连接关闭失败: {}", connectionId, channelFuture.cause());
                future.completeExceptionally(channelFuture.cause());
            }
        });
    }

    @Override
    public String getOutboundId() {
        return outboundId;
    }

    @Override
    public OutboundConfig getConfig() {
        return config;
    }

    @Override
    public String getType() {
        return OutboundHandlerType.TCP_DIRECT.getType();
    }

    @Override
    public HealthStatus getHealthStatus() {
        return healthStatus;
    }

    @Override
    public CompletableFuture<HealthStatus> healthCheck() {
        // 简单的健康检查：基于成功率
        long total = successCounter.get() + failureCounter.get();
        if (total == 0) {
            return CompletableFuture.completedFuture(HealthStatus.HEALTHY);
        }

        double successRate = (double) successCounter.get() / total;
        HealthStatus status;

        if (successRate >= 0.9) {
            status = HealthStatus.HEALTHY;
        } else if (successRate >= 0.5) {
            status = HealthStatus.DEGRADED;
        } else {
            status = HealthStatus.UNHEALTHY;
        }

        this.healthStatus = status;
        return CompletableFuture.completedFuture(status);
    }

    @Override
    public OutboundStatistics getStatistics() {
        return new OutboundStatistics(
                connectionCounter.get(),
                successCounter.get(),
                failureCounter.get(),
                0 // 当前活跃连接数需要额外统计
        );
    }

    @Override
    public void resetStatistics() {
        connectionCounter.set(0);
        successCounter.set(0);
        failureCounter.set(0);
    }

    /**
     * 更新健康状态
     */
    private void updateHealthStatus(boolean success) {
        // 基于最近的连接成功率动态更新健康状态
        long total = successCounter.get() + failureCounter.get();
        if (total > 0) {
            double successRate = (double) successCounter.get() / total;

            if (successRate >= 0.9) {
                healthStatus = HealthStatus.HEALTHY;
            } else if (successRate >= 0.5) {
                healthStatus = HealthStatus.DEGRADED;
            } else {
                healthStatus = HealthStatus.UNHEALTHY;
            }
        }
    }
}