package com.xiang.proxy.server;

import com.xiang.proxy.server.core.ProxyServerInitializer;
import com.xiang.proxy.server.config.ProxyServerConfigManager;
import com.xiang.proxy.server.config.properties.ProxyServerProperties;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.ssl.SslContextManager;
import com.xiang.proxy.server.util.ThreadPoolPerformanceAnalyzer;
import com.xiang.proxy.server.util.MemoryOptimizer;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.WriteBufferWaterMark;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 代理服务器主类
 * 接收来自代理客户端的请求，转发给目标服务器
 */
public class ProxyServer {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServer.class);

    private final int port;
    private ScheduledExecutorService monitoringExecutor;
    
    public ProxyServer(int port) {
        this.port = port;
    }
    
    public void start() throws InterruptedException {
        // Get configuration
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        ProxyServerProperties.PerformanceProperties perfConfig = configManager.getProperties().getPerformance();

        // Configure boss thread count with intelligent defaults
        int bossThreads = calculateOptimalBossThreads(perfConfig.getBossThreads());

        // Configure worker thread count with I/O-optimized algorithm
        int workerThreads = calculateOptimalWorkerThreads(perfConfig.getWorkerThreads());

        // Create thread factories with meaningful names and optimized settings
        ThreadFactory bossThreadFactory = createOptimizedThreadFactory("proxy-server-boss", Thread.NORM_PRIORITY + 1);
        ThreadFactory workerThreadFactory = createOptimizedThreadFactory("proxy-server-worker", Thread.NORM_PRIORITY);
        
        // Create event loop groups with configurable thread counts
        EventLoopGroup bossGroup = new NioEventLoopGroup(bossThreads, bossThreadFactory);
        EventLoopGroup workerGroup = new NioEventLoopGroup(workerThreads, workerThreadFactory);

        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ProxyServerInitializer())
                    // 服务器端优化选项
                    .option(ChannelOption.SO_BACKLOG, calculateOptimalBacklog())
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                    .option(ChannelOption.SO_RCVBUF, 256 * 1024) // 256KB接收缓冲区
                    // 客户端连接优化选项
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.SO_REUSEADDR, true)
                    .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                    .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000) // 减少连接超时
                    .childOption(ChannelOption.SO_RCVBUF, calculateOptimalBufferSize())
                    .childOption(ChannelOption.SO_SNDBUF, calculateOptimalBufferSize())
                    .childOption(ChannelOption.WRITE_BUFFER_WATER_MARK, calculateOptimalWaterMark())
                    .childOption(ChannelOption.SO_LINGER, 0) // 快速关闭连接
                    .childOption(ChannelOption.AUTO_READ, true) // 自动读取数据
                    .childOption(ChannelOption.AUTO_CLOSE, true); // 自动关闭连接

            ChannelFuture future = bootstrap.bind(port).sync();
            logger.info("代理服务器启动成功，监听端口: {}，Boss线程数: {}，Worker线程数: {}", 
                       port, bossThreads, workerThreads);

            // Show SSL configuration information
            SslContextManager sslManager = SslContextManager.getInstance();
            logger.info("SSL配置: {}", sslManager.getSslConfigSummary());


            // Start performance monitoring task
            startPerformanceMonitoring();

            // Start thread pool performance monitoring
            ThreadPoolPerformanceAnalyzer.getInstance().startMonitoring();
            logger.info("线程池性能监控已启动");

            // Add shutdown hook
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("正在关闭代理服务器...");
                ThreadPoolPerformanceAnalyzer.getInstance().stopMonitoring();
                if (monitoringExecutor != null) {
                    monitoringExecutor.shutdown();
                }
            }));

            future.channel().closeFuture().sync();
        } finally {
            if (monitoringExecutor != null) {
                monitoringExecutor.shutdown();
            }
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
        }
    }

    /**
     * 启动性能监控任务
     */
    private void startPerformanceMonitoring() {
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();

        // Check if performance monitoring is enabled
        if (!configManager.isMetricsEnabled()) {
            logger.info("性能监控已禁用");
            return;
        }

        monitoringExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "PerformanceMonitor");
            t.setDaemon(true);
            return t;
        });

        // Use interval from configuration file
        int intervalSeconds = configManager.getMetricsReportIntervalSeconds();
        monitoringExecutor.scheduleWithFixedDelay(() -> {
            try {
                PerformanceMetrics.getInstance().logMetrics();
                ThreadPoolPerformanceAnalyzer.getInstance().logPerformanceReport();
                MemoryOptimizer.getInstance().logMemoryStats();
                AdvancedMetrics.getInstance().logDetailedReport();

                // 检查是否需要垃圾回收
                if (MemoryOptimizer.getInstance().shouldPerformGC()) {
                    MemoryOptimizer.getInstance().suggestGC();
                }
            } catch (Exception e) {
                logger.warn("性能监控任务执行异常: {}", e.getMessage());
            }
        }, intervalSeconds, intervalSeconds, TimeUnit.SECONDS);

        logger.info("性能监控任务已启动，每{}秒输出一次统计信息", intervalSeconds);
    }
    
    public static void main(String[] args) throws InterruptedException {
        // Parse command line arguments
        parseCommandLineArgs(args);

        // Initialize all configuration files and components at startup
        initializeComponents();

        // Get default port from configuration manager
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        int port = configManager.getServerPort();

        // Command line arguments can override the port in the configuration file
        if (args.length > 0) {
            try {
                // If the first argument is a number, use it as the port number
                port = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                // If the first argument is not a number, it might be a config file path, port number might be in the second argument
                if (args.length > 1) {
                    try {
                        port = Integer.parseInt(args[1]);
                    } catch (NumberFormatException ex) {
                        // Keep using the port from the configuration file
                    }
                }
            }
        }

        new ProxyServer(port).start();
    }

    /**
     * 启动时初始化所有组件和配置文件
     */
    private static void initializeComponents() {
        logger.info("开始初始化服务器组件和配置文件...");

        try {
            // 1. Initialize GeoIPUtil（触发IP段数据加载和在线更新）
            logger.info("初始化地理位置IP判断工具...");
            com.xiang.proxy.server.util.GeoIPUtil.getInstance();

            // 2. Initialize GeoLocationFilter（触发恶意内容和白名单加载）
            logger.info("初始化地理位置过滤器...");
            com.xiang.proxy.server.filter.GeoLocationFilter.getInstance();

            logger.info("服务器组件和配置文件初始化完成");

        } catch (Exception e) {
            logger.error("初始化服务器组件失败", e);
            // Don't exit because of initialization failure, let the server continue to start
            logger.warn("将使用默认配置继续启动服务器");
        }
    }

    /**
     * 解析命令行参数
     * 支持的格式：
     * - java ProxyServer [port]
     * - java ProxyServer --config=path/to/config.yml [port]
     * - java ProxyServer -c path/to/config.yml [port]
     */
    private static void parseCommandLineArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            // Handle --config=path format
            if (arg.startsWith("--config=")) {
                String configPath = arg.substring("--config=".length());
                ProxyServerConfigManager.setConfigFilePath(configPath);
                continue;
            }

            // Handle -c path format
            if (arg.equals("-c") && i + 1 < args.length) {
                String configPath = args[i + 1];
                ProxyServerConfigManager.setConfigFilePath(configPath);
                i++; // Skip the next argument, because it is the config file path
                continue;
            }
        }
    }

    /**
     * 计算最优的Boss线程数
     * Boss线程主要负责接受连接，通常1个就足够，但在高并发场景下可以适当增加
     */
    private int calculateOptimalBossThreads(int configuredThreads) {
        if (configuredThreads > 0) {
            return configuredThreads;
        }

        // 对于代理服务器，通常1个Boss线程就足够
        // 只有在极高并发场景下才需要多个Boss线程
        int cpuCores = Runtime.getRuntime().availableProcessors();
        if (cpuCores >= 16) {
            // 高性能服务器，可以使用2个Boss线程
            return 2;
        } else {
            // 普通服务器，1个Boss线程足够
            return 1;
        }
    }

    /**
     * 计算最优的Worker线程数
     * 针对I/O密集型代理服务器进行优化
     */
    private int calculateOptimalWorkerThreads(int configuredThreads) {
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        ProxyServerProperties.PerformanceProperties perfConfig = configManager.getProperties().getPerformance();

        if (configuredThreads > 0) {
            return configuredThreads;
        }

        // 如果禁用了线程优化，使用简单的计算方式
        if (!perfConfig.isEnableThreadOptimization()) {
            int cpuCores = Runtime.getRuntime().availableProcessors();
            return Math.max(perfConfig.getMinWorkerThreads(), cpuCores * 2);
        }

        int cpuCores = Runtime.getRuntime().availableProcessors();
        long maxMemory = Runtime.getRuntime().maxMemory();
        int ioRatio = perfConfig.getIoRatio();

        // 基于I/O比例的智能计算
        // I/O比例越高，需要的线程数越多
        double ioMultiplier = 1.0 + (ioRatio / 100.0) * 7.0; // I/O比例从1倍到8倍
        int baseThreads = (int) (cpuCores * ioMultiplier);

        // 根据可用内存调整线程数
        long memoryPerThreadMB = 2; // 2MB per thread (stack + overhead)
        long availableMemoryMB = maxMemory / (1024 * 1024);
        int memoryLimitedThreads = (int) (availableMemoryMB / memoryPerThreadMB / 4); // 保留3/4内存给其他用途

        // 取较小值，但确保最小值
        int optimalThreads = Math.min(baseThreads, memoryLimitedThreads);
        optimalThreads = Math.max(optimalThreads, perfConfig.getMinWorkerThreads());

        // 应用最大线程数限制
        if (perfConfig.getMaxWorkerThreads() > 0) {
            optimalThreads = Math.min(optimalThreads, perfConfig.getMaxWorkerThreads());
        } else {
            // 默认上限：避免过多线程导致上下文切换开销
            int defaultMaxThreads = cpuCores * 8;
            optimalThreads = Math.min(optimalThreads, defaultMaxThreads);
        }

        logger.info("智能线程数计算: CPU核心数={}, 可用内存={}MB, I/O比例={}%, I/O倍数={:.2f}, " +
                   "基础线程数={}, 内存限制线程数={}, 最小线程数={}, 最大线程数={}, 最终线程数={}",
                   cpuCores, availableMemoryMB, ioRatio, ioMultiplier, baseThreads, memoryLimitedThreads,
                   perfConfig.getMinWorkerThreads(),
                   perfConfig.getMaxWorkerThreads() > 0 ? perfConfig.getMaxWorkerThreads() : "无限制",
                   optimalThreads);

        return optimalThreads;
    }

    /**
     * 创建优化的线程工厂
     */
    private ThreadFactory createOptimizedThreadFactory(String namePrefix, int priority) {
        return new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            private final String prefix = namePrefix + "-";

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, prefix + threadNumber.getAndIncrement());

                // 设置为守护线程，避免阻止JVM关闭
                thread.setDaemon(true);

                // 设置线程优先级
                thread.setPriority(priority);

                // 设置未捕获异常处理器
                thread.setUncaughtExceptionHandler((t, e) -> {
                    logger.error("线程 {} 发生未捕获异常", t.getName(), e);
                });

                return thread;
            }
        };
    }

    /**
     * 计算最优的backlog大小
     */
    private int calculateOptimalBacklog() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 基于CPU核心数和预期并发连接数计算
        int baseBacklog = Math.max(1024, cpuCores * 256);
        // 限制在合理范围内
        return Math.min(baseBacklog, 8192);
    }

    /**
     * 计算最优的缓冲区大小
     */
    private int calculateOptimalBufferSize() {
        // 根据系统内存和网络带宽计算最优缓冲区大小
        long maxMemory = Runtime.getRuntime().maxMemory();
        long availableMemory = maxMemory / (1024 * 1024); // MB

        int bufferSize;
        if (availableMemory > 4096) { // > 4GB
            bufferSize = 128 * 1024; // 128KB
        } else if (availableMemory > 2048) { // > 2GB
            bufferSize = 96 * 1024;  // 96KB
        } else if (availableMemory > 1024) { // > 1GB
            bufferSize = 64 * 1024;  // 64KB
        } else {
            bufferSize = 32 * 1024;  // 32KB
        }

        logger.debug("计算缓冲区大小: 可用内存={}MB, 缓冲区大小={}KB",
                    availableMemory, bufferSize / 1024);

        return bufferSize;
    }

    /**
     * 计算最优的写缓冲区水位标记
     */
    private WriteBufferWaterMark calculateOptimalWaterMark() {
        int bufferSize = calculateOptimalBufferSize();

        // 低水位标记：缓冲区大小的1/8
        int lowWaterMark = bufferSize / 8;
        // 高水位标记：缓冲区大小的1/2
        int highWaterMark = bufferSize / 2;

        // 确保最小值
        lowWaterMark = Math.max(lowWaterMark, 8 * 1024);   // 最小8KB
        highWaterMark = Math.max(highWaterMark, 32 * 1024); // 最小32KB

        logger.debug("计算写缓冲区水位: 低水位={}KB, 高水位={}KB",
                    lowWaterMark / 1024, highWaterMark / 1024);

        return new WriteBufferWaterMark(lowWaterMark, highWaterMark);
    }
}